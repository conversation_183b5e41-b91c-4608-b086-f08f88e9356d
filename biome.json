{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": [".next", ".vercel", ".vscode", "node_modules", "./src/payload-types.ts", "./src/app/(payload)/admin/importMap.js"]}, "formatter": {"enabled": true, "indentStyle": "tab", "lineWidth": 130}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "warn"}, "correctness": {"useExhaustiveDependencies": "warn", "noUnknownMediaFeatureName": "off"}, "a11y": {"useKeyWithClickEvents": "warn", "useValidAnchor": "warn"}, "nursery": {"useSortedClasses": "warn"}, "style": {"noNonNullAssertion": "off", "noUnusedTemplateLiteral": "off"}}}, "css": {"linter": {"enabled": true}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "parser": {"cssModules": true}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "overrides": [{"include": ["**/*.css"], "linter": {"rules": {"correctness": {"noUnknownFunction": "off"}}}}]}