html {
  --scrollbar-gutter: 0px;

  &.lenis-stopped {
    --scrollbar-gutter: var(--scrollbar-width);
  }
}

* {
  scrollbar-width: thin;
}

body {
  min-height: 100vh;
  overscroll-behavior: none;
  background-color: var(--color-primary);
  color: var(--color-secondary);
  display: flex;
  flex-direction: column;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Selection styling */
*::selection {
  background-color: var(--color-contrast);
  color: var(--color-primary);
}

/* SVG icon colors */
svg.icon {
  path[fill],
  rect[fill],
  circle[fill] {
    fill: currentColor;
  }
  path[stroke],
  rect[stroke],
  circle[stroke] {
    stroke: currentColor;
  }
}

/* Hover states */
.link {
  @media (--hover) {
    &:hover {
      text-decoration: underline;
    }
  }
}

/* Focus states */
*:focus-visible {
  outline: 2px solid var(--color-contrast);
}

[data-hidden-on-init="true"] {
  opacity: 0;
  will-change: opacity;
}

.link {
  position: relative;
}

.link:before {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: currentColor;
  border-radius: 0.25rem;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.35s var(--ease-in-out-expo);
  will-change: transform;
}

.link:hover:before {
  transform-origin: left;
  transform: scaleX(1);
}


h1, .h1 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: clamp(4em, 5vw, 7.5em);
  font-weight: 300;
  line-height: 1;
}

h2, .h2 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 5em;
  font-weight: 300;
  line-height: 1.05;
}

h3, .h3 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 2.5em;
  font-weight: 300;
  line-height: 1.1;
}

h4, .h4 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 1.75em;
  font-weight: 300;
  line-height: 1.15;
}

h5, .h5 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 1.25em;
  font-weight: 300;
  line-height: 1.2;
}

p, .p {
  margin-bottom: 0;
  margin-top: 0;
  font-size: clamp(1em, 1vw, 1.5em);
  font-weight: 400;
  line-height: 1.4;
}