@import "tailwindcss";

@theme {
	--breakpoint-*: initial;
	--breakpoint-dt: 800px;

  --color-*: initial;
	--color-primary: #ffffff;
	--color-secondary: #000000;
	--color-contrast: #BB1B01;
  --color-black: #000000;
	--color-white: #ffffff;
	--color-red: #BB1B01;
    
  /* --spacing-*: initial; */
  --spacing-0: 0;
	--spacing-safe: var(--safe);
	--spacing-gap: var(--gap);
  --spacing-header-height: var(--header-height);

  --font-*: initial;
  --font-sans: var(--font-sans);

  --ease-*: initial;
  --ease-in-quad: cubic-bezier(0.55, 0.085, 0.68, 0.53);
	--ease-in-cubic: cubic-bezier(0.55, 0.055, 0.675, 0.19);
	--ease-in-quart: cubic-bezier(0.895, 0.03, 0.685, 0.22);
	--ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
	--ease-in-expo: cubic-bezier(0.95, 0.05, 0.795, 0.035);
	--ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.335);
	--ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
	--ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
	--ease-out-quart: cubic-bezier(0.165, 0.84, 0.44, 1);
	--ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
	--ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
	--ease-out-circ: cubic-bezier(0.075, 0.82, 0.165, 1);
	--ease-in-out-quad: cubic-bezier(0.455, 0.03, 0.515, 0.955);
	--ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
	--ease-in-out-quart: cubic-bezier(0.77, 0, 0.175, 1);
	--ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);
	--ease-in-out-expo: cubic-bezier(1, 0, 0, 1);
	--ease-in-out-circ: cubic-bezier(0.785, 0.135, 0.15, 0.86);
	--ease-gleasing: cubic-bezier(0.4, 0, 0, 1);

  --width-columns-3: calc((3 * var(--column-width)) + (2 * var(--gap)));
  --width-columns-4: calc((4 * var(--column-width)) + (3 * var(--gap)));
  --width-columns-5: calc((5 * var(--column-width)) + (4 * var(--gap)));
  --width-columns-6: calc((6 * var(--column-width)) + (5 * var(--gap)));
}

/** Custom theme overwrites **/
[data-theme=light] {
  --color-primary: #ffffff;
	--color-secondary: #000000;
	--color-contrast: #BB1B01;
}

@media (prefers-color-scheme: dark) {
  :root {
	--color-primary: #0a0a0a;
		--color-secondary: #ffffff;
		--color-contrast: #BB1B01;
  }
}

@utility desktop-only {
  @media (--mobile) {
    display: none !important;
  }
}

@utility mobile-only {
  @media (--desktop) {
    display: none !important;
  }
}

@utility dr-grid {
	display: grid;
	grid-template-columns: repeat(var(--columns), 1fr);
	gap: var(--gap);
}

@utility dr-layout-block {
	margin-inline: auto;
	width: calc(100% - 2 * var(--safe));
}

@utility dr-layout-block-inner {
	padding-inline: var(--safe);
	width: 100%;
}

@utility dr-layout-grid {
	@apply dr-layout-block dr-grid;
}

@utility dr-layout-grid-inner {
	@apply dr-layout-block-inner dr-grid;
}

/** Custom variants **/
@custom-variant light (&:where([data-theme=light], [data-theme=light *]));
@custom-variant dark (&:where([data-theme=dark], [data-theme=dark *]));
@custom-variant red (&:where([data-theme=red], [data-theme=red *]));

/** Custom function utilities **/
@utility dr-text-* {
	font-size: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-text-px {
	font-size: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-text-* {
	font-size: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-text-px {
	font-size: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-tracking-* {
	letter-spacing: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-tracking-px {
	letter-spacing: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-tracking-* {
	letter-spacing: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-tracking-px {
	letter-spacing: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-leading-* {
	line-height: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-leading-px {
	line-height: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-leading-* {
	line-height: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-leading-px {
	line-height: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-border-* {
	border-width: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-border-px {
	border-width: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-border-* {
	border-width: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-border-px {
	border-width: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-border-t-* {
	border-top-width: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-border-t-px {
	border-top-width: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-border-t-* {
	border-top-width: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-border-t-px {
	border-top-width: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-border-r-* {
	border-right-width: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-border-r-px {
	border-right-width: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-border-r-* {
	border-right-width: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-border-r-px {
	border-right-width: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-border-b-* {
	border-bottom-width: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-border-b-px {
	border-bottom-width: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-border-b-* {
	border-bottom-width: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-border-b-px {
	border-bottom-width: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-border-l-* {
	border-left-width: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-border-l-px {
	border-left-width: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-border-l-* {
	border-left-width: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-border-l-px {
	border-left-width: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-rounded-* {
	border-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-rounded-px {
	border-radius: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-rounded-* {
	border-radius: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-rounded-px {
	border-radius: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-rounded-t-* {
	border-top-left-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
border-top-right-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-rounded-t-px {
	border-top-left-radius: calc(100 / var(--device-width) * 1vw);
border-top-right-radius: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-rounded-t-* {
	border-top-left-radius: calc((--value(integer) * -100) / var(--device-width) * 1vw);
border-top-right-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility -dr-rounded-t-px {
	border-top-left-radius: calc(-100 / var(--device-width) * 1vw);
border-top-right-radius: calc(100 / var(--device-width) * 1vw);
}

@utility dr-rounded-r-* {
	border-top-right-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
border-bottom-right-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-rounded-r-px {
	border-top-right-radius: calc(100 / var(--device-width) * 1vw);
border-bottom-right-radius: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-rounded-r-* {
	border-top-right-radius: calc((--value(integer) * -100) / var(--device-width) * 1vw);
border-bottom-right-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility -dr-rounded-r-px {
	border-top-right-radius: calc(-100 / var(--device-width) * 1vw);
border-bottom-right-radius: calc(100 / var(--device-width) * 1vw);
}

@utility dr-rounded-b-* {
	border-bottom-right-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
border-bottom-left-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-rounded-b-px {
	border-bottom-right-radius: calc(100 / var(--device-width) * 1vw);
border-bottom-left-radius: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-rounded-b-* {
	border-bottom-right-radius: calc((--value(integer) * -100) / var(--device-width) * 1vw);
border-bottom-left-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility -dr-rounded-b-px {
	border-bottom-right-radius: calc(-100 / var(--device-width) * 1vw);
border-bottom-left-radius: calc(100 / var(--device-width) * 1vw);
}

@utility dr-rounded-l-* {
	border-bottom-left-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
border-top-left-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-rounded-l-px {
	border-bottom-left-radius: calc(100 / var(--device-width) * 1vw);
border-top-left-radius: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-rounded-l-* {
	border-bottom-left-radius: calc((--value(integer) * -100) / var(--device-width) * 1vw);
border-top-left-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility -dr-rounded-l-px {
	border-bottom-left-radius: calc(-100 / var(--device-width) * 1vw);
border-top-left-radius: calc(100 / var(--device-width) * 1vw);
}

@utility dr-rounded-tl-* {
	border-top-left-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-rounded-tl-px {
	border-top-left-radius: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-rounded-tl-* {
	border-top-left-radius: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-rounded-tl-px {
	border-top-left-radius: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-rounded-tr-* {
	border-top-right-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-rounded-tr-px {
	border-top-right-radius: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-rounded-tr-* {
	border-top-right-radius: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-rounded-tr-px {
	border-top-right-radius: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-rounded-br-* {
	border-bottom-right-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-rounded-br-px {
	border-bottom-right-radius: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-rounded-br-* {
	border-bottom-right-radius: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-rounded-br-px {
	border-bottom-right-radius: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-rounded-bl-* {
	border-bottom-left-radius: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-rounded-bl-px {
	border-bottom-left-radius: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-rounded-bl-* {
	border-bottom-left-radius: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-rounded-bl-px {
	border-bottom-left-radius: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-w-* {
	width: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-w-px {
	width: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-w-* {
	width: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-w-px {
	width: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-min-w-* {
	min-width: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-min-w-px {
	min-width: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-min-w-* {
	min-width: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-min-w-px {
	min-width: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-max-w-* {
	max-width: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-max-w-px {
	max-width: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-max-w-* {
	max-width: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-max-w-px {
	max-width: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-h-* {
	height: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-h-px {
	height: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-h-* {
	height: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-h-px {
	height: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-min-h-* {
	min-height: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-min-h-px {
	min-height: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-min-h-* {
	min-height: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-min-h-px {
	min-height: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-max-h-* {
	max-height: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-max-h-px {
	max-height: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-max-h-* {
	max-height: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-max-h-px {
	max-height: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-gap-* {
	gap: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-gap-px {
	gap: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-gap-* {
	gap: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-gap-px {
	gap: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-gap-x-* {
	column-gap: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-gap-x-px {
	column-gap: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-gap-x-* {
	column-gap: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-gap-x-px {
	column-gap: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-gap-y-* {
	row-gap: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-gap-y-px {
	row-gap: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-gap-y-* {
	row-gap: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-gap-y-px {
	row-gap: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-p-* {
	padding: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-p-px {
	padding: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-p-* {
	padding: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-p-px {
	padding: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-px-* {
	padding-inline: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-px-px {
	padding-inline: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-px-* {
	padding-inline: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-px-px {
	padding-inline: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-py-* {
	padding-block: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-py-px {
	padding-block: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-py-* {
	padding-block: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-py-px {
	padding-block: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-pt-* {
	padding-top: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-pt-px {
	padding-top: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-pt-* {
	padding-top: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-pt-px {
	padding-top: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-pr-* {
	padding-right: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-pr-px {
	padding-right: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-pr-* {
	padding-right: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-pr-px {
	padding-right: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-pl-* {
	padding-left: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-pl-px {
	padding-left: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-pl-* {
	padding-left: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-pl-px {
	padding-left: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-pb-* {
	padding-bottom: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-pb-px {
	padding-bottom: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-pb-* {
	padding-bottom: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-pb-px {
	padding-bottom: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-m-* {
	margin: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-m-px {
	margin: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-m-* {
	margin: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-m-px {
	margin: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-mx-* {
	margin-inline: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-mx-px {
	margin-inline: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-mx-* {
	margin-inline: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-mx-px {
	margin-inline: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-my-* {
	margin-block: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-my-px {
	margin-block: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-my-* {
	margin-block: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-my-px {
	margin-block: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-mt-* {
	margin-top: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-mt-px {
	margin-top: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-mt-* {
	margin-top: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-mt-px {
	margin-top: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-mr-* {
	margin-right: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-mr-px {
	margin-right: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-mr-* {
	margin-right: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-mr-px {
	margin-right: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-ml-* {
	margin-left: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-ml-px {
	margin-left: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-ml-* {
	margin-left: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-ml-px {
	margin-left: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-mb-* {
	margin-bottom: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-mb-px {
	margin-bottom: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-mb-* {
	margin-bottom: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-mb-px {
	margin-bottom: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-top-* {
	top: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-top-px {
	top: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-top-* {
	top: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-top-px {
	top: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-right-* {
	right: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-right-px {
	right: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-right-* {
	right: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-right-px {
	right: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-bottom-* {
	bottom: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-bottom-px {
	bottom: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-bottom-* {
	bottom: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-bottom-px {
	bottom: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-left-* {
	left: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-left-px {
	left: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-left-* {
	left: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-left-px {
	left: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-inset-* {
	inset: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-inset-px {
	inset: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-inset-* {
	inset: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-inset-px {
	inset: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-inset-x-* {
	inset-inline: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-inset-x-px {
	inset-inline: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-inset-x-* {
	inset-inline: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-inset-x-px {
	inset-inline: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-inset-y-* {
	inset-block: calc((--value(integer) * 100) / var(--device-width) * 1vw);
}
@utility dr-inset-y-px {
	inset-block: calc(100 / var(--device-width) * 1vw);
}
@utility -dr-inset-y-* {
	inset-block: calc((--value(integer) * -100) / var(--device-width) * 1vw);
}
@utility -dr-inset-y-px {
	inset-block: calc(-100 / var(--device-width) * 1vw);
}

@utility dr-w-col-* {
	width: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-w-col-value {
	width: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-w-col-* {
	width: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-w-col--value {
	width: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-min-w-col-* {
	min-width: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-min-w-col-value {
	min-width: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-min-w-col-* {
	min-width: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-min-w-col--value {
	min-width: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-max-w-col-* {
	max-width: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-max-w-col-value {
	max-width: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-max-w-col-* {
	max-width: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-max-w-col--value {
	max-width: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-h-col-* {
	height: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-h-col-value {
	height: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-h-col-* {
	height: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-h-col--value {
	height: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-min-h-col-* {
	min-height: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-min-h-col-value {
	min-height: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-min-h-col-* {
	min-height: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-min-h-col--value {
	min-height: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-max-h-col-* {
	max-height: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-max-h-col-value {
	max-height: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-max-h-col-* {
	max-height: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-max-h-col--value {
	max-height: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-gap-col-* {
	gap: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-gap-col-value {
	gap: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-gap-col-* {
	gap: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-gap-col--value {
	gap: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-gap-x-col-* {
	column-gap: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-gap-x-col-value {
	column-gap: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-gap-x-col-* {
	column-gap: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-gap-x-col--value {
	column-gap: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-gap-y-col-* {
	row-gap: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-gap-y-col-value {
	row-gap: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-gap-y-col-* {
	row-gap: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-gap-y-col--value {
	row-gap: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-p-col-* {
	padding: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-p-col-value {
	padding: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-p-col-* {
	padding: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-p-col--value {
	padding: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-px-col-* {
	padding-inline: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-px-col-value {
	padding-inline: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-px-col-* {
	padding-inline: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-px-col--value {
	padding-inline: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-py-col-* {
	padding-block: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-py-col-value {
	padding-block: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-py-col-* {
	padding-block: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-py-col--value {
	padding-block: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-pt-col-* {
	padding-top: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-pt-col-value {
	padding-top: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-pt-col-* {
	padding-top: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-pt-col--value {
	padding-top: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-pr-col-* {
	padding-right: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-pr-col-value {
	padding-right: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-pr-col-* {
	padding-right: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-pr-col--value {
	padding-right: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-pl-col-* {
	padding-left: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-pl-col-value {
	padding-left: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-pl-col-* {
	padding-left: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-pl-col--value {
	padding-left: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-pb-col-* {
	padding-bottom: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-pb-col-value {
	padding-bottom: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-pb-col-* {
	padding-bottom: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-pb-col--value {
	padding-bottom: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-m-col-* {
	margin: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-m-col-value {
	margin: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-m-col-* {
	margin: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-m-col--value {
	margin: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-mx-col-* {
	margin-inline: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-mx-col-value {
	margin-inline: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-mx-col-* {
	margin-inline: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-mx-col--value {
	margin-inline: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-my-col-* {
	margin-block: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-my-col-value {
	margin-block: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-my-col-* {
	margin-block: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-my-col--value {
	margin-block: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-mt-col-* {
	margin-top: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-mt-col-value {
	margin-top: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-mt-col-* {
	margin-top: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-mt-col--value {
	margin-top: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-mr-col-* {
	margin-right: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-mr-col-value {
	margin-right: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-mr-col-* {
	margin-right: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-mr-col--value {
	margin-right: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-ml-col-* {
	margin-left: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-ml-col-value {
	margin-left: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-ml-col-* {
	margin-left: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-ml-col--value {
	margin-left: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-mb-col-* {
	margin-bottom: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-mb-col-value {
	margin-bottom: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-mb-col-* {
	margin-bottom: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-mb-col--value {
	margin-bottom: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-top-col-* {
	top: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-top-col-value {
	top: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-top-col-* {
	top: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-top-col--value {
	top: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-right-col-* {
	right: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-right-col-value {
	right: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-right-col-* {
	right: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-right-col--value {
	right: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-bottom-col-* {
	bottom: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-bottom-col-value {
	bottom: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-bottom-col-* {
	bottom: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-bottom-col--value {
	bottom: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-left-col-* {
	left: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-left-col-value {
	left: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-left-col-* {
	left: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-left-col--value {
	left: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-inset-col-* {
	inset: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-inset-col-value {
	inset: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-inset-col-* {
	inset: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-inset-col--value {
	inset: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-inset-x-col-* {
	inset-inline: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-inset-x-col-value {
	inset-inline: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-inset-x-col-* {
	inset-inline: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-inset-x-col--value {
	inset-inline: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}

@utility dr-inset-y-col-* {
	inset-block: calc((--value(integer) * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility dr-inset-y-col-value {
	inset-block: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}
@utility -dr-inset-y-col-* {
	inset-block: calc((--value(integer) * -1 * var(--column-width)) + ((--value(integer) - 1) * var(--gap)));
}
@utility -dr-inset-y-col--value {
	inset-block: calc((value * var(--column-width)) + ((value - 1) * var(--gap)));
}