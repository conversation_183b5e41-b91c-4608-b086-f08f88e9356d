"use client";

import type { LenisOptions } from "lenis";
import "lenis/dist/lenis.css";
import type { LenisRef, LenisProps as ReactLenisProps } from "lenis/react";
import { ReactLenis, useLenis } from "lenis/react";
import { useEffect, useRef } from "react";
import { useTempus } from "tempus/react";
import { useStore } from "@/lib/store";

interface LenisProps extends Omit<ReactLenisProps, "ref"> {
	root: boolean;
	options: LenisOptions;
}

export function Lenis({ root, options }: LenisProps) {
	const lenisRef = useRef<LenisRef>(null);
	const isNavOpened = useStore((state) => state.isNavOpened);
	const lenis = useLenis();

	useTempus((time: number) => {
		if (lenisRef.current?.lenis) {
			lenisRef.current.lenis.raf(time);
		}
	});

	useEffect(() => {
		if (isNavOpened) {
			lenis?.stop();
		} else {
			lenis?.start();
		}
	}, [isNavOpened, lenis]);

	return (
		<ReactLenis
			ref={lenisRef}
			root={root}
			options={{
				...options,
				lerp: options?.lerp ?? 0.125,
				autoRaf: false,
				anchors: true,
				prevent: (node: Element | null) =>
					node?.nodeName === "VERCEL-LIVE-FEEDBACK" ||
					node?.id === "theatrejs-studio-root",
			}}
		/>
	);
}
